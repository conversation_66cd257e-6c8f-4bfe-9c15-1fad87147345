export default function Dashboard() {
    return (
        <div className="flex flex-1 flex-col gap-4">
            <div className="grid auto-rows-min gap-4 md:grid-cols-3">
                <div className="bg-muted/50 aspect-video rounded-xl flex items-center justify-center">
                    <div className="text-center">
                        <h3 className="text-lg font-semibold">Total Sales</h3>
                        <p className="text-2xl font-bold text-green-600">₹45,231</p>
                        <p className="text-sm text-muted-foreground">This month</p>
                    </div>
                </div>
                <div className="bg-muted/50 aspect-video rounded-xl flex items-center justify-center">
                    <div className="text-center">
                        <h3 className="text-lg font-semibold">Products</h3>
                        <p className="text-2xl font-bold text-blue-600">1,234</p>
                        <p className="text-sm text-muted-foreground">In inventory</p>
                    </div>
                </div>
                <div className="bg-muted/50 aspect-video rounded-xl flex items-center justify-center">
                    <div className="text-center">
                        <h3 className="text-lg font-semibold">Customers</h3>
                        <p className="text-2xl font-bold text-purple-600">567</p>
                        <p className="text-sm text-muted-foreground">Active customers</p>
                    </div>
                </div>
            </div>
            <div className="bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min">
                <div className="p-6">
                    <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
                    <p className="text-muted-foreground mb-6">
                        Welcome to your Kirana Shop dashboard. This is where you can manage your inventory,
                        track sales, and monitor your business performance.
                    </p>
                    <div className="grid gap-4 md:grid-cols-2">
                        <div className="p-4 border rounded-lg">
                            <h3 className="font-semibold mb-2">Recent Sales</h3>
                            <p className="text-sm text-muted-foreground">View your latest transactions and sales data.</p>
                        </div>
                        <div className="p-4 border rounded-lg">
                            <h3 className="font-semibold mb-2">Low Stock Alert</h3>
                            <p className="text-sm text-muted-foreground">Monitor products that need restocking.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}