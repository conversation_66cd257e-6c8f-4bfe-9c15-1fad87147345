import dotenv from "dotenv";
import { AppDataSource } from "./dbconfig";
import express from "express";
import cors from "cors";

dotenv.config();

const app = express();

// Global middlewares
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Simple API endpoint
app.get('/api', (req, res) => {
    res.json({
        message: 'Kirana Shop API',
        version: '1.0.0',
        timestamp: new Date().toISOString()
    });
});

// 404 handler for undefined routes
app.use('*', (req, res) => {
    res.status(404).json({
        message: `Route ${req.method} ${req.originalUrl} not found`,
        timestamp: new Date().toISOString()
    });
});

AppDataSource.initialize()
    .then(() => {
        console.log('📦 Connected to DB');
        const PORT = process.env.PORT || 5000;
        app.listen(PORT, () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
            console.log(`📋 Health check available at http://localhost:${PORT}/health`);
        });
    })
    .catch((error) => console.error('❌ DB Connection Error: ', error));